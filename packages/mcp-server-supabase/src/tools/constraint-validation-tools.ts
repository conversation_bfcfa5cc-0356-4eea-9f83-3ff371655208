import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';
import {
  schemaNameSchema,
  tableNameSchema,
  columnNameSchema,
  postgresIdentifierSchema,
  safeParseWithDetails,
  formatValidationErrors,
} from './crud-validation.js';

export type ConstraintValidationToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

/**
 * Constraint validation result schema
 */
const constraintValidationResultSchema = z.object({
  constraint_name: z.string(),
  constraint_type: z.enum(['FOREIGN KEY', 'CHECK', 'UNIQUE', 'PRIMARY KEY', 'NOT NULL']),
  table_schema: z.string(),
  table_name: z.string(),
  column_name: z.string().nullable(),
  is_valid: z.boolean(),
  violation_count: z.number(),
  violation_details: z.array(z.record(z.unknown())).optional(),
  error_message: z.string().nullable(),
});

export type ConstraintValidationResult = z.infer<typeof constraintValidationResultSchema>;

export function getConstraintValidationTools({
  platform,
  projectId,
  readOnly,
}: ConstraintValidationToolsOptions) {
  const project_id = projectId;

  const constraintValidationTools = {
    validate_foreign_key_constraints: injectableTool({
      description: 'Validate foreign key constraints in specified tables or entire schemas. Identifies orphaned records and constraint violations.',
      parameters: z.object({
        project_id: z.string(),
        schema: schemaNameSchema.default('public').describe('Schema name to validate (default: public)'),
        table: tableNameSchema.optional().describe('Specific table to validate (optional, validates all tables in schema if not provided)'),
        constraint_name: postgresIdentifierSchema.optional().describe('Specific constraint name to validate (optional)'),
        include_violation_details: z.boolean().default(false).describe('Include detailed violation records in response'),
        limit_violations: z.number().min(1).max(1000).default(100).describe('Maximum number of violation details to return'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, constraint_name, include_violation_details, limit_violations }) => {
        // Enhanced validation using safe parsing
        const schemaValidation = safeParseWithDetails(schemaNameSchema, schema, 'Schema name');
        if (!schemaValidation.success) {
          throw new Error(schemaValidation.error);
        }

        if (table) {
          const tableValidation = safeParseWithDetails(tableNameSchema, table, 'Table name');
          if (!tableValidation.success) {
            throw new Error(tableValidation.error);
          }
        }

        if (constraint_name) {
          const constraintValidation = safeParseWithDetails(postgresIdentifierSchema, constraint_name, 'Constraint name');
          if (!constraintValidation.success) {
            throw new Error(constraintValidation.error);
          }
        }

        // Build the foreign key validation query
        let whereClause = `tc.table_schema = '${schema}'`;
        if (table) {
          whereClause += ` AND tc.table_name = '${table}'`;
        }
        if (constraint_name) {
          whereClause += ` AND tc.constraint_name = '${constraint_name}'`;
        }

        const query = `
          SELECT
            tc.constraint_name,
            'FOREIGN KEY' as constraint_type,
            tc.table_schema,
            tc.table_name,
            kcu.column_name,
            true as is_valid,
            0 as violation_count,
            NULL as error_message
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          WHERE tc.constraint_type = 'FOREIGN KEY'
            AND ${whereClause}
          ORDER BY tc.table_schema, tc.table_name, tc.constraint_name;
        `;

        try {
          const data = await platform.executeSql(project_id, {
            query,
            read_only: true,
          });

          const results: ConstraintValidationResult[] = [];

          // For each foreign key constraint, check for violations
          for (const row of data) {
            try {
              // Get the foreign table information
              const foreignTableQuery = `
                SELECT
                  ccu.table_schema AS foreign_table_schema,
                  ccu.table_name AS foreign_table_name,
                  ccu.column_name AS foreign_column_name
                FROM information_schema.constraint_column_usage ccu
                WHERE ccu.constraint_name = '${row.constraint_name}'
                  AND ccu.constraint_schema = '${row.table_schema}';
              `;

              const foreignTableInfo = await platform.executeSql(project_id, {
                query: foreignTableQuery,
                read_only: true,
              });

              if (foreignTableInfo.length === 0) {
                // No foreign table info found
                const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                  constraint_name: row.constraint_name,
                  constraint_type: 'FOREIGN KEY',
                  table_schema: row.table_schema,
                  table_name: row.table_name,
                  column_name: row.column_name,
                  is_valid: false,
                  violation_count: 0,
                  violation_details: undefined,
                  error_message: 'Could not find foreign table information for constraint',
                });
                results.push(result);
                continue;
              }

              const foreignTable = foreignTableInfo[0];

              // Check for orphaned records
              const violationQuery = `
                SELECT COUNT(*) as violation_count
                FROM ${row.table_schema}.${row.table_name} child
                WHERE child.${row.column_name} IS NOT NULL
                  AND NOT EXISTS (
                    SELECT 1
                    FROM ${foreignTable.foreign_table_schema}.${foreignTable.foreign_table_name} parent
                    WHERE parent.${foreignTable.foreign_column_name} = child.${row.column_name}
                  );
              `;

              const violationCountResult = await platform.executeSql(project_id, {
                query: violationQuery,
                read_only: true,
              });

              const violationCount = violationCountResult[0]?.violation_count || 0;
              let violationDetails = undefined;

              // If violations exist and details are requested, fetch sample records
              if (include_violation_details && violationCount > 0) {
                try {
                  const detailQuery = `
                    SELECT *
                    FROM ${row.table_schema}.${row.table_name} child
                    WHERE child.${row.column_name} IS NOT NULL
                      AND NOT EXISTS (
                        SELECT 1
                        FROM ${foreignTable.foreign_table_schema}.${foreignTable.foreign_table_name} parent
                        WHERE parent.${foreignTable.foreign_column_name} = child.${row.column_name}
                      )
                    LIMIT ${limit_violations};
                  `;

                  violationDetails = await platform.executeSql(project_id, {
                    query: detailQuery,
                    read_only: true,
                  });
                } catch (detailError) {
                  // If we can't fetch details, continue without them
                }
              }

              const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                constraint_name: row.constraint_name,
                constraint_type: 'FOREIGN KEY',
                table_schema: row.table_schema,
                table_name: row.table_name,
                column_name: row.column_name,
                is_valid: violationCount === 0,
                violation_count: violationCount,
                violation_details: violationDetails,
                error_message: violationCount > 0 ? `Found ${violationCount} orphaned records violating foreign key constraint` : null,
              });

              results.push(result);
            } catch (error) {
              // If we can't check this constraint, mark it as having an error
              const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                constraint_name: row.constraint_name,
                constraint_type: 'FOREIGN KEY',
                table_schema: row.table_schema,
                table_name: row.table_name,
                column_name: row.column_name,
                is_valid: false,
                violation_count: 0,
                violation_details: undefined,
                error_message: `Could not validate foreign key constraint: ${error instanceof Error ? error.message : String(error)}`,
              });
              results.push(result);
            }
          }

          return {
            schema,
            table: table || 'all',
            constraint_name: constraint_name || 'all',
            total_constraints_checked: results.length,
            valid_constraints: results.filter(r => r.is_valid).length,
            invalid_constraints: results.filter(r => !r.is_valid).length,
            total_violations: results.reduce((sum, r) => sum + r.violation_count, 0),
            results
          };
        } catch (error) {
          throw new Error(`Failed to validate foreign key constraints: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    validate_unique_constraints: injectableTool({
      description: 'Validate unique constraints in specified tables or entire schemas. Identifies duplicate values that violate uniqueness.',
      parameters: z.object({
        project_id: z.string(),
        schema: schemaNameSchema.default('public').describe('Schema name to validate (default: public)'),
        table: tableNameSchema.optional().describe('Specific table to validate (optional, validates all tables in schema if not provided)'),
        constraint_name: postgresIdentifierSchema.optional().describe('Specific constraint name to validate (optional)'),
        include_violation_details: z.boolean().default(false).describe('Include detailed violation records in response'),
        limit_violations: z.number().min(1).max(1000).default(100).describe('Maximum number of violation details to return'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, constraint_name, include_violation_details, limit_violations }) => {
        // Enhanced validation using safe parsing
        const schemaValidation = safeParseWithDetails(schemaNameSchema, schema, 'Schema name');
        if (!schemaValidation.success) {
          throw new Error(schemaValidation.error);
        }

        if (table) {
          const tableValidation = safeParseWithDetails(tableNameSchema, table, 'Table name');
          if (!tableValidation.success) {
            throw new Error(tableValidation.error);
          }
        }

        if (constraint_name) {
          const constraintValidation = safeParseWithDetails(postgresIdentifierSchema, constraint_name, 'Constraint name');
          if (!constraintValidation.success) {
            throw new Error(constraintValidation.error);
          }
        }

        // Build the unique constraint validation query
        let whereClause = `tc.table_schema = '${schema}'`;
        if (table) {
          whereClause += ` AND tc.table_name = '${table}'`;
        }
        if (constraint_name) {
          whereClause += ` AND tc.constraint_name = '${constraint_name}'`;
        }

        const query = `
          WITH unique_constraints AS (
            SELECT 
              tc.constraint_name,
              tc.table_schema,
              tc.table_name,
              array_agg(kcu.column_name ORDER BY kcu.ordinal_position) as column_names
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
              ON tc.constraint_name = kcu.constraint_name 
              AND tc.table_schema = kcu.table_schema
            WHERE tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY')
              AND ${whereClause}
            GROUP BY tc.constraint_name, tc.table_schema, tc.table_name
          )
          SELECT 
            uc.constraint_name,
            CASE 
              WHEN tc.constraint_type = 'PRIMARY KEY' THEN 'PRIMARY KEY'
              ELSE 'UNIQUE'
            END as constraint_type,
            uc.table_schema,
            uc.table_name,
            array_to_string(uc.column_names, ', ') as column_name,
            true as is_valid,
            0 as violation_count,
            NULL as error_message
          FROM unique_constraints uc
          JOIN information_schema.table_constraints tc 
            ON uc.constraint_name = tc.constraint_name 
            AND uc.table_schema = tc.table_schema
          ORDER BY uc.table_schema, uc.table_name, uc.constraint_name;
        `;

        try {
          const data = await platform.executeSql(project_id, {
            query,
            read_only: true,
          });

          const results: ConstraintValidationResult[] = [];

          // For each unique constraint, check for violations
          for (const row of data) {
            const columnNames = row.column_name.split(', ');
            const columnList = columnNames.join(', ');
            
            // Check for duplicates
            const duplicateQuery = `
              SELECT ${columnList}, COUNT(*) as duplicate_count
              FROM ${row.table_schema}.${row.table_name}
              WHERE ${columnNames.map(col => `${col} IS NOT NULL`).join(' AND ')}
              GROUP BY ${columnList}
              HAVING COUNT(*) > 1
              ORDER BY duplicate_count DESC
              LIMIT ${limit_violations};
            `;

            try {
              const duplicates = await platform.executeSql(project_id, {
                query: duplicateQuery,
                read_only: true,
              });

              const violationCount = duplicates.reduce((sum: number, dup: any) => sum + (dup.duplicate_count - 1), 0);

              const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                constraint_name: row.constraint_name,
                constraint_type: row.constraint_type,
                table_schema: row.table_schema,
                table_name: row.table_name,
                column_name: row.column_name,
                is_valid: violationCount === 0,
                violation_count: violationCount,
                violation_details: include_violation_details && violationCount > 0 ? duplicates : undefined,
                error_message: violationCount > 0 ? `Found ${violationCount} duplicate values violating uniqueness constraint` : null,
              });

              results.push(result);
            } catch (error) {
              // If we can't check this constraint, mark it as having an error
              const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                constraint_name: row.constraint_name,
                constraint_type: row.constraint_type,
                table_schema: row.table_schema,
                table_name: row.table_name,
                column_name: row.column_name,
                is_valid: false,
                violation_count: 0,
                violation_details: undefined,
                error_message: `Could not validate constraint: ${error instanceof Error ? error.message : String(error)}`,
              });
              results.push(result);
            }
          }

          return {
            schema,
            table: table || 'all',
            constraint_name: constraint_name || 'all',
            total_constraints_checked: results.length,
            valid_constraints: results.filter(r => r.is_valid).length,
            invalid_constraints: results.filter(r => !r.is_valid).length,
            total_violations: results.reduce((sum, r) => sum + r.violation_count, 0),
            results
          };
        } catch (error) {
          throw new Error(`Failed to validate unique constraints: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    validate_check_constraints: injectableTool({
      description: 'Validate check constraints in specified tables or entire schemas. Identifies records that violate check constraint conditions.',
      parameters: z.object({
        project_id: z.string(),
        schema: schemaNameSchema.default('public').describe('Schema name to validate (default: public)'),
        table: tableNameSchema.optional().describe('Specific table to validate (optional, validates all tables in schema if not provided)'),
        constraint_name: postgresIdentifierSchema.optional().describe('Specific constraint name to validate (optional)'),
        include_violation_details: z.boolean().default(false).describe('Include detailed violation records in response'),
        limit_violations: z.number().min(1).max(1000).default(100).describe('Maximum number of violation details to return'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, constraint_name, include_violation_details, limit_violations }) => {
        // Enhanced validation using safe parsing
        const schemaValidation = safeParseWithDetails(schemaNameSchema, schema, 'Schema name');
        if (!schemaValidation.success) {
          throw new Error(schemaValidation.error);
        }

        if (table) {
          const tableValidation = safeParseWithDetails(tableNameSchema, table, 'Table name');
          if (!tableValidation.success) {
            throw new Error(tableValidation.error);
          }
        }

        if (constraint_name) {
          const constraintValidation = safeParseWithDetails(postgresIdentifierSchema, constraint_name, 'Constraint name');
          if (!constraintValidation.success) {
            throw new Error(constraintValidation.error);
          }
        }

        // Build the check constraint validation query
        let whereClause = `tc.table_schema = '${schema}'`;
        if (table) {
          whereClause += ` AND tc.table_name = '${table}'`;
        }
        if (constraint_name) {
          whereClause += ` AND tc.constraint_name = '${constraint_name}'`;
        }

        const query = `
          SELECT
            tc.constraint_name,
            'CHECK' as constraint_type,
            tc.table_schema,
            tc.table_name,
            NULL as column_name,
            cc.check_clause,
            true as is_valid,
            0 as violation_count,
            NULL as error_message
          FROM information_schema.table_constraints tc
          JOIN information_schema.check_constraints cc
            ON tc.constraint_name = cc.constraint_name
            AND tc.constraint_schema = cc.constraint_schema
          WHERE tc.constraint_type = 'CHECK'
            AND ${whereClause}
          ORDER BY tc.table_schema, tc.table_name, tc.constraint_name;
        `;

        try {
          const data = await platform.executeSql(project_id, {
            query,
            read_only: true,
          });

          const results: ConstraintValidationResult[] = [];

          // For each check constraint, validate it by trying to find violations
          for (const row of data) {
            try {
              // Create a query to find records that violate the check constraint
              // We do this by negating the check clause condition
              const checkClause = row.check_clause;

              // Simple approach: count records that would violate the constraint
              // by using NOT (check_clause)
              const violationQuery = `
                SELECT COUNT(*) as violation_count
                FROM ${row.table_schema}.${row.table_name}
                WHERE NOT (${checkClause});
              `;

              const violationCountResult = await platform.executeSql(project_id, {
                query: violationQuery,
                read_only: true,
              });

              const violationCount = violationCountResult[0]?.violation_count || 0;
              let violationDetails = undefined;

              // If violations exist and details are requested, fetch sample records
              if (include_violation_details && violationCount > 0) {
                try {
                  const detailQuery = `
                    SELECT *
                    FROM ${row.table_schema}.${row.table_name}
                    WHERE NOT (${checkClause})
                    LIMIT ${limit_violations};
                  `;

                  violationDetails = await platform.executeSql(project_id, {
                    query: detailQuery,
                    read_only: true,
                  });
                } catch (detailError) {
                  // If we can't fetch details, continue without them
                }
              }

              const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                constraint_name: row.constraint_name,
                constraint_type: 'CHECK',
                table_schema: row.table_schema,
                table_name: row.table_name,
                column_name: null,
                is_valid: violationCount === 0,
                violation_count: violationCount,
                violation_details: violationDetails,
                error_message: violationCount > 0 ? `Found ${violationCount} records violating check constraint: ${checkClause}` : null,
              });

              results.push(result);
            } catch (error) {
              // If we can't validate this constraint, mark it as having an error
              const result: ConstraintValidationResult = constraintValidationResultSchema.parse({
                constraint_name: row.constraint_name,
                constraint_type: 'CHECK',
                table_schema: row.table_schema,
                table_name: row.table_name,
                column_name: null,
                is_valid: false,
                violation_count: 0,
                violation_details: undefined,
                error_message: `Could not validate check constraint: ${error instanceof Error ? error.message : String(error)}`,
              });
              results.push(result);
            }
          }

          return {
            schema,
            table: table || 'all',
            constraint_name: constraint_name || 'all',
            total_constraints_checked: results.length,
            valid_constraints: results.filter(r => r.is_valid).length,
            invalid_constraints: results.filter(r => !r.is_valid).length,
            total_violations: results.reduce((sum, r) => sum + r.violation_count, 0),
            results
          };
        } catch (error) {
          throw new Error(`Failed to validate check constraints: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    validate_all_constraints: injectableTool({
      description: 'Comprehensive validation of all constraint types (foreign key, unique, check) in specified tables or entire schemas.',
      parameters: z.object({
        project_id: z.string(),
        schema: schemaNameSchema.default('public').describe('Schema name to validate (default: public)'),
        table: tableNameSchema.optional().describe('Specific table to validate (optional, validates all tables in schema if not provided)'),
        constraint_types: z.array(z.enum(['foreign_key', 'unique', 'check'])).default(['foreign_key', 'unique', 'check']).describe('Types of constraints to validate'),
        include_violation_details: z.boolean().default(false).describe('Include detailed violation records in response'),
        limit_violations: z.number().min(1).max(1000).default(100).describe('Maximum number of violation details to return per constraint'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, constraint_types, include_violation_details, limit_violations }) => {
        const results = {
          schema,
          table: table || 'all',
          constraint_types_checked: constraint_types,
          summary: {
            total_constraints_checked: 0,
            valid_constraints: 0,
            invalid_constraints: 0,
            total_violations: 0,
          },
          foreign_key_results: [] as ConstraintValidationResult[],
          unique_results: [] as ConstraintValidationResult[],
          check_results: [] as ConstraintValidationResult[],
        };

        // Get reference to the constraint validation tools
        const tools = getConstraintValidationTools({ platform, projectId, readOnly });

        try {
          // Validate foreign key constraints if requested
          if (constraint_types.includes('foreign_key')) {
            const fkResult = await tools.validate_foreign_key_constraints.execute({
              project_id,
              schema,
              table,
              include_violation_details,
              limit_violations,
            });
            results.foreign_key_results = fkResult.results;
            results.summary.total_constraints_checked += fkResult.total_constraints_checked;
            results.summary.valid_constraints += fkResult.valid_constraints;
            results.summary.invalid_constraints += fkResult.invalid_constraints;
            results.summary.total_violations += fkResult.total_violations;
          }

          // Validate unique constraints if requested
          if (constraint_types.includes('unique')) {
            const uniqueResult = await tools.validate_unique_constraints.execute({
              project_id,
              schema,
              table,
              include_violation_details,
              limit_violations,
            });
            results.unique_results = uniqueResult.results;
            results.summary.total_constraints_checked += uniqueResult.total_constraints_checked;
            results.summary.valid_constraints += uniqueResult.valid_constraints;
            results.summary.invalid_constraints += uniqueResult.invalid_constraints;
            results.summary.total_violations += uniqueResult.total_violations;
          }

          // Validate check constraints if requested
          if (constraint_types.includes('check')) {
            const checkResult = await tools.validate_check_constraints.execute({
              project_id,
              schema,
              table,
              include_violation_details,
              limit_violations,
            });
            results.check_results = checkResult.results;
            results.summary.total_constraints_checked += checkResult.total_constraints_checked;
            results.summary.valid_constraints += checkResult.valid_constraints;
            results.summary.invalid_constraints += checkResult.invalid_constraints;
            results.summary.total_violations += checkResult.total_violations;
          }

          return results;
        } catch (error) {
          throw new Error(`Failed to validate constraints: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),
  };

  return constraintValidationTools;
}
