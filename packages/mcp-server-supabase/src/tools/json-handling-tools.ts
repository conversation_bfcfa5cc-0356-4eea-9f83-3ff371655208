import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';
import {
  schemaNameSchema,
  tableNameSchema,
  columnNameSchema,
  postgresIdentifierSchema,
  safeParseWithDetails,
  formatValidationErrors,
} from './crud-validation.js';

export type JsonHandlingToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

/**
 * JSON validation result schema
 */
const jsonValidationResultSchema = z.object({
  is_valid: z.boolean(),
  error_message: z.string().nullable(),
  error_position: z.number().nullable(),
  syntax_errors: z.array(z.string()).optional(),
  validation_details: z.record(z.unknown()).optional(),
});

export type JsonValidationResult = z.infer<typeof jsonValidationResultSchema>;

/**
 * JSON Schema validation schema
 */
const jsonSchemaValidationSchema = z.object({
  json_data: z.string().describe('JSON string to validate'),
  schema_definition: z.record(z.unknown()).optional().describe('JSON Schema definition to validate against'),
  strict_mode: z.boolean().default(false).describe('Enable strict validation mode'),
});

/**
 * JSON path extraction schema
 */
const jsonPathExtractionSchema = z.object({
  json_data: z.string().describe('JSON string to extract from'),
  path: z.string().describe('JSON path expression (e.g., "$.user.address.city")'),
  default_value: z.unknown().optional().describe('Default value if path not found'),
});

export function getJsonHandlingTools({
  platform,
  projectId,
  readOnly,
}: JsonHandlingToolsOptions) {
  const project_id = projectId;

  const jsonHandlingTools = {
    validate_json_syntax: injectableTool({
      description: 'Validate JSON syntax and structure. Checks for basic JSON syntax errors and provides detailed error information.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to validate'),
        check_encoding: z.boolean().default(true).describe('Check for encoding issues'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, check_encoding }) => {
        try {
          // Enhanced validation using safe parsing
          const jsonDataValidation = safeParseWithDetails(z.string().min(1), json_data, 'JSON data');
          if (!jsonDataValidation.success) {
            throw new Error(jsonDataValidation.error);
          }

          // Use PostgreSQL's json_valid function for validation
          const query = `
            SELECT 
              CASE 
                WHEN $1::text IS NULL OR $1::text = '' THEN false
                ELSE (
                  SELECT CASE 
                    WHEN $1::json IS NOT NULL THEN true
                    ELSE false
                  END
                )
              END as is_valid,
              CASE 
                WHEN $1::text IS NULL OR $1::text = '' THEN 'Empty or null JSON data'
                ELSE NULL
              END as error_message
          `;

          const result = await platform.query(query, [json_data]);
          
          if (result.rows.length === 0) {
            return {
              is_valid: false,
              error_message: 'No validation result returned',
              error_position: null,
              syntax_errors: ['Validation query failed'],
            };
          }

          const validationResult = result.rows[0];
          
          // Additional encoding checks if requested
          const encodingIssues: string[] = [];
          if (check_encoding && json_data) {
            // Check for common encoding issues
            if (json_data.includes('\uFFFD')) {
              encodingIssues.push('Contains replacement characters (encoding issues detected)');
            }
            if (!/^[\x00-\x7F]*$/.test(json_data) && !/^[\u0000-\uFFFF]*$/.test(json_data)) {
              encodingIssues.push('Contains invalid Unicode characters');
            }
          }

          return {
            is_valid: validationResult.is_valid,
            error_message: validationResult.error_message,
            error_position: null, // PostgreSQL doesn't provide position info
            syntax_errors: encodingIssues.length > 0 ? encodingIssues : undefined,
            validation_details: {
              input_length: json_data.length,
              encoding_checked: check_encoding,
              encoding_issues: encodingIssues.length,
            },
          };

        } catch (error) {
          // Parse PostgreSQL JSON error for more details
          const errorMessage = error instanceof Error ? error.message : String(error);
          let errorPosition: number | null = null;
          const syntaxErrors: string[] = [];

          // Extract position from PostgreSQL error if available
          const positionMatch = errorMessage.match(/at character (\d+)/);
          if (positionMatch) {
            errorPosition = parseInt(positionMatch[1], 10);
          }

          // Extract specific syntax errors
          if (errorMessage.includes('invalid input syntax')) {
            syntaxErrors.push('Invalid JSON syntax');
          }
          if (errorMessage.includes('unexpected character')) {
            syntaxErrors.push('Unexpected character in JSON');
          }
          if (errorMessage.includes('unterminated')) {
            syntaxErrors.push('Unterminated JSON structure');
          }

          return {
            is_valid: false,
            error_message: errorMessage,
            error_position: errorPosition,
            syntax_errors: syntaxErrors.length > 0 ? syntaxErrors : ['JSON parsing failed'],
            validation_details: {
              input_length: json_data.length,
              encoding_checked: check_encoding,
            },
          };
        }
      },
    }),

    validate_json_schema: injectableTool({
      description: 'Validate JSON data against a JSON Schema definition. Supports JSON Schema Draft 7 validation.',
      parameters: z.object({
        project_id: z.string(),
        ...jsonSchemaValidationSchema.shape,
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, schema_definition, strict_mode }) => {
        try {
          // First validate JSON syntax
          const syntaxValidation = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data,
            check_encoding: true,
          });

          if (!syntaxValidation.is_valid) {
            return {
              is_valid: false,
              error_message: `JSON syntax error: ${syntaxValidation.error_message}`,
              error_position: syntaxValidation.error_position,
              syntax_errors: syntaxValidation.syntax_errors,
              schema_validation_errors: [],
            };
          }

          // If no schema provided, return syntax validation result
          if (!schema_definition) {
            return {
              is_valid: true,
              error_message: null,
              error_position: null,
              syntax_errors: [],
              schema_validation_errors: [],
              validation_details: {
                schema_provided: false,
                syntax_only: true,
              },
            };
          }

          // For now, we'll implement basic schema validation using PostgreSQL JSON functions
          // In a full implementation, you'd use a proper JSON Schema validator
          const parsedData = JSON.parse(json_data);
          const schemaErrors: string[] = [];

          // Basic schema validation checks
          if (schema_definition.type) {
            const actualType = Array.isArray(parsedData) ? 'array' : typeof parsedData;
            if (actualType !== schema_definition.type) {
              schemaErrors.push(`Expected type '${schema_definition.type}', got '${actualType}'`);
            }
          }

          if (schema_definition.required && Array.isArray(schema_definition.required)) {
            for (const requiredField of schema_definition.required) {
              if (typeof parsedData === 'object' && parsedData !== null && !(requiredField in parsedData)) {
                schemaErrors.push(`Missing required field: '${requiredField}'`);
              }
            }
          }

          return {
            is_valid: schemaErrors.length === 0,
            error_message: schemaErrors.length > 0 ? schemaErrors.join('; ') : null,
            error_position: null,
            syntax_errors: [],
            schema_validation_errors: schemaErrors,
            validation_details: {
              schema_provided: true,
              strict_mode,
              schema_type: schema_definition.type,
              required_fields: schema_definition.required || [],
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            is_valid: false,
            error_message: `Schema validation error: ${errorMessage}`,
            error_position: null,
            syntax_errors: [],
            schema_validation_errors: [errorMessage],
          };
        }
      },
    }),

    extract_json_value: injectableTool({
      description: 'Extract values from JSON data using path expressions. Supports dot notation and array indexing.',
      parameters: z.object({
        project_id: z.string(),
        ...jsonPathExtractionSchema.shape,
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, path, default_value }) => {
        try {
          // Validate JSON syntax first
          const syntaxValidation = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data,
            check_encoding: false,
          });

          if (!syntaxValidation.is_valid) {
            throw new Error(`Invalid JSON: ${syntaxValidation.error_message}`);
          }

          // Convert dot notation to PostgreSQL JSON path
          let pgPath = path;
          if (path.startsWith('$.')) {
            pgPath = path.substring(2); // Remove $.
          }

          // Use PostgreSQL's json_extract_path_text function
          const pathParts = pgPath.split('.').filter(part => part.length > 0);
          const query = `
            SELECT json_extract_path_text($1::json, ${pathParts.map((_, i) => `$${i + 2}`).join(', ')}) as extracted_value
          `;

          const params = [json_data, ...pathParts];
          const result = await platform.query(query, params);

          if (result.rows.length === 0) {
            return {
              success: false,
              value: default_value,
              path_found: false,
              error_message: 'No result returned from extraction query',
            };
          }

          const extractedValue = result.rows[0].extracted_value;
          const pathFound = extractedValue !== null;

          return {
            success: true,
            value: pathFound ? extractedValue : default_value,
            path_found: pathFound,
            error_message: null,
            extraction_details: {
              original_path: path,
              postgresql_path: pathParts,
              path_depth: pathParts.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            value: default_value,
            path_found: false,
            error_message: errorMessage,
          };
        }
      },
    }),

    get_json_validation_errors: injectableTool({
      description: 'Get detailed validation errors for malformed JSON data. Provides comprehensive error analysis.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to analyze for errors'),
        include_suggestions: z.boolean().default(true).describe('Include repair suggestions'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, include_suggestions }) => {
        try {
          // First try basic validation
          const syntaxValidation = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data,
            check_encoding: true,
          });

          if (syntaxValidation.is_valid) {
            return {
              has_errors: false,
              error_count: 0,
              errors: [],
              suggestions: [],
              analysis: {
                json_length: json_data.length,
                structure_valid: true,
              },
            };
          }

          // Analyze common JSON errors
          const errors: Array<{
            type: string;
            message: string;
            position?: number;
            line?: number;
            column?: number;
          }> = [];

          const suggestions: string[] = [];

          // Check for common syntax issues
          if (json_data.includes('"') && !json_data.match(/^".*"$/)) {
            // Check for unmatched quotes
            const quoteCount = (json_data.match(/"/g) || []).length;
            if (quoteCount % 2 !== 0) {
              errors.push({
                type: 'unmatched_quotes',
                message: 'Unmatched quotation marks detected',
              });
              if (include_suggestions) {
                suggestions.push('Check for missing or extra quotation marks');
              }
            }
          }

          // Check for trailing commas
          if (json_data.includes(',}') || json_data.includes(',]')) {
            errors.push({
              type: 'trailing_comma',
              message: 'Trailing comma detected',
            });
            if (include_suggestions) {
              suggestions.push('Remove trailing commas before closing brackets');
            }
          }

          // Check for unmatched brackets
          const openBraces = (json_data.match(/{/g) || []).length;
          const closeBraces = (json_data.match(/}/g) || []).length;
          const openBrackets = (json_data.match(/\[/g) || []).length;
          const closeBrackets = (json_data.match(/]/g) || []).length;

          if (openBraces !== closeBraces) {
            errors.push({
              type: 'unmatched_braces',
              message: `Unmatched braces: ${openBraces} opening, ${closeBraces} closing`,
            });
            if (include_suggestions) {
              suggestions.push('Check for missing or extra curly braces {}');
            }
          }

          if (openBrackets !== closeBrackets) {
            errors.push({
              type: 'unmatched_brackets',
              message: `Unmatched brackets: ${openBrackets} opening, ${closeBrackets} closing`,
            });
            if (include_suggestions) {
              suggestions.push('Check for missing or extra square brackets []');
            }
          }

          // Check for unquoted keys (common mistake)
          const unquotedKeyPattern = /{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g;
          const unquotedKeys = [...json_data.matchAll(unquotedKeyPattern)];
          if (unquotedKeys.length > 0) {
            errors.push({
              type: 'unquoted_keys',
              message: 'Unquoted object keys detected',
            });
            if (include_suggestions) {
              suggestions.push('Wrap object keys in double quotes');
            }
          }

          return {
            has_errors: errors.length > 0,
            error_count: errors.length,
            errors,
            suggestions: include_suggestions ? suggestions : [],
            analysis: {
              json_length: json_data.length,
              structure_valid: false,
              brace_balance: openBraces === closeBraces,
              bracket_balance: openBrackets === closeBrackets,
              quote_issues: errors.some(e => e.type === 'unmatched_quotes'),
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            has_errors: true,
            error_count: 1,
            errors: [{
              type: 'analysis_error',
              message: `Error during analysis: ${errorMessage}`,
            }],
            suggestions: include_suggestions ? ['Unable to analyze - check JSON format'] : [],
            analysis: {
              json_length: json_data.length,
              structure_valid: false,
              analysis_failed: true,
            },
          };
        }
      },
    }),

    safe_json_parse: injectableTool({
      description: 'Safely parse JSON strings without throwing exceptions. Returns parsed data or error information.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to parse'),
        return_raw_on_error: z.boolean().default(false).describe('Return raw string if parsing fails'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, return_raw_on_error }) => {
        try {
          // First validate syntax
          const syntaxValidation = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data,
            check_encoding: true,
          });

          if (!syntaxValidation.is_valid) {
            return {
              success: false,
              data: return_raw_on_error ? json_data : null,
              error_message: syntaxValidation.error_message,
              is_raw_fallback: return_raw_on_error,
            };
          }

          // Use PostgreSQL to parse and return the JSON
          const query = `SELECT $1::json as parsed_data`;
          const result = await platform.query(query, [json_data]);

          if (result.rows.length === 0) {
            return {
              success: false,
              data: return_raw_on_error ? json_data : null,
              error_message: 'No result returned from parsing query',
              is_raw_fallback: return_raw_on_error,
            };
          }

          return {
            success: true,
            data: result.rows[0].parsed_data,
            error_message: null,
            is_raw_fallback: false,
            parsing_details: {
              input_length: json_data.length,
              output_type: typeof result.rows[0].parsed_data,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            data: return_raw_on_error ? json_data : null,
            error_message: errorMessage,
            is_raw_fallback: return_raw_on_error,
          };
        }
      },
    }),

    extract_multiple_values: injectableTool({
      description: 'Extract multiple values from JSON data using multiple path expressions in a single operation.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to extract from'),
        paths: z.array(z.object({
          name: z.string().describe('Name for this extraction'),
          path: z.string().describe('JSON path expression'),
          default_value: z.unknown().optional().describe('Default value if path not found'),
        })).describe('Array of path extraction specifications'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, paths }) => {
        try {
          // Validate JSON syntax first
          const syntaxValidation = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data,
            check_encoding: false,
          });

          if (!syntaxValidation.is_valid) {
            return {
              success: false,
              extractions: {},
              error_message: `Invalid JSON: ${syntaxValidation.error_message}`,
              paths_processed: 0,
            };
          }

          const extractions: Record<string, {
            value: unknown;
            path_found: boolean;
            error?: string;
          }> = {};

          let pathsProcessed = 0;
          const errors: string[] = [];

          // Process each path extraction
          for (const pathSpec of paths) {
            try {
              const extractionResult = await jsonHandlingTools.extract_json_value.execute({
                project_id,
                json_data,
                path: pathSpec.path,
                default_value: pathSpec.default_value,
              });

              extractions[pathSpec.name] = {
                value: extractionResult.value,
                path_found: extractionResult.path_found,
                error: extractionResult.error_message || undefined,
              };

              pathsProcessed++;
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              extractions[pathSpec.name] = {
                value: pathSpec.default_value,
                path_found: false,
                error: errorMessage,
              };
              errors.push(`${pathSpec.name}: ${errorMessage}`);
            }
          }

          return {
            success: errors.length === 0,
            extractions,
            error_message: errors.length > 0 ? errors.join('; ') : null,
            paths_processed: pathsProcessed,
            extraction_summary: {
              total_paths: paths.length,
              successful_extractions: Object.values(extractions).filter(e => e.path_found).length,
              failed_extractions: errors.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            extractions: {},
            error_message: errorMessage,
            paths_processed: 0,
          };
        }
      },
    }),

    get_json_keys: injectableTool({
      description: 'Get all keys from a JSON object at a specified depth level. Useful for exploring JSON structure.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to analyze'),
        max_depth: z.number().min(1).max(10).default(1).describe('Maximum depth to traverse (1-10)'),
        include_array_indices: z.boolean().default(false).describe('Include array indices as keys'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, max_depth, include_array_indices }) => {
        try {
          // Validate JSON syntax first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              keys: [],
              error_message: parseResult.error_message,
              structure_info: null,
            };
          }

          // Use PostgreSQL's json_object_keys function for top level
          const query = `
            SELECT json_object_keys($1::json) as key_name
            WHERE json_typeof($1::json) = 'object'
          `;

          const result = await platform.query(query, [json_data]);
          const topLevelKeys = result.rows.map(row => row.key_name);

          // For deeper analysis, we'd need to implement recursive traversal
          // For now, we'll provide basic key extraction
          const structureInfo = {
            is_object: topLevelKeys.length > 0,
            is_array: false,
            top_level_key_count: topLevelKeys.length,
            max_depth_analyzed: 1, // Currently only analyzing top level
          };

          // Check if it's an array
          const arrayCheckQuery = `SELECT json_typeof($1::json) = 'array' as is_array`;
          const arrayResult = await platform.query(arrayCheckQuery, [json_data]);
          if (arrayResult.rows.length > 0) {
            structureInfo.is_array = arrayResult.rows[0].is_array;
          }

          return {
            success: true,
            keys: topLevelKeys,
            error_message: null,
            structure_info: structureInfo,
            extraction_details: {
              max_depth_requested: max_depth,
              include_array_indices,
              analysis_method: 'postgresql_json_object_keys',
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            keys: [],
            error_message: errorMessage,
            structure_info: null,
          };
        }
      },
    }),

    merge_json_objects: injectableTool({
      description: 'Merge multiple JSON objects into a single object. Supports deep merging and conflict resolution.',
      parameters: z.object({
        project_id: z.string(),
        json_objects: z.array(z.string()).min(2).describe('Array of JSON object strings to merge'),
        merge_strategy: z.enum(['overwrite', 'preserve_first', 'array_combine']).default('overwrite').describe('Strategy for handling conflicts'),
        deep_merge: z.boolean().default(true).describe('Enable deep merging of nested objects'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_objects, merge_strategy, deep_merge }) => {
        try {
          // Validate all JSON objects first
          const parsedObjects = [];
          for (let i = 0; i < json_objects.length; i++) {
            const parseResult = await jsonHandlingTools.safe_json_parse.execute({
              project_id,
              json_data: json_objects[i],
              return_raw_on_error: false,
            });

            if (!parseResult.success) {
              return {
                success: false,
                merged_json: null,
                error_message: `Invalid JSON at index ${i}: ${parseResult.error_message}`,
                objects_processed: i,
              };
            }

            // Ensure it's an object
            if (typeof parseResult.data !== 'object' || Array.isArray(parseResult.data) || parseResult.data === null) {
              return {
                success: false,
                merged_json: null,
                error_message: `Object at index ${i} is not a JSON object`,
                objects_processed: i,
              };
            }

            parsedObjects.push(parseResult.data);
          }

          // Use PostgreSQL's jsonb_build_object and || operator for merging
          // For simple merging, we'll use the || operator
          let mergeQuery = `SELECT $1::jsonb`;
          const params = [JSON.stringify(parsedObjects[0])];

          for (let i = 1; i < parsedObjects.length; i++) {
            if (merge_strategy === 'preserve_first') {
              // Preserve first object's values
              mergeQuery += ` || $${i + 1}::jsonb`;
            } else {
              // Overwrite with later values (default)
              mergeQuery = `SELECT $${i + 1}::jsonb || (${mergeQuery})`;
            }
            params.push(JSON.stringify(parsedObjects[i]));
          }

          mergeQuery += ' as merged_result';

          const result = await platform.query(mergeQuery, params);

          if (result.rows.length === 0) {
            return {
              success: false,
              merged_json: null,
              error_message: 'No result returned from merge operation',
              objects_processed: json_objects.length,
            };
          }

          return {
            success: true,
            merged_json: result.rows[0].merged_result,
            error_message: null,
            objects_processed: json_objects.length,
            merge_details: {
              strategy_used: merge_strategy,
              deep_merge_enabled: deep_merge,
              input_count: json_objects.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            merged_json: null,
            error_message: errorMessage,
            objects_processed: 0,
          };
        }
      },
    }),

    filter_json_properties: injectableTool({
      description: 'Filter JSON object properties based on specified criteria. Remove or keep specific properties.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON object string to filter'),
        properties: z.array(z.string()).describe('Array of property names to filter'),
        filter_mode: z.enum(['include', 'exclude']).default('include').describe('Whether to include or exclude specified properties'),
        recursive: z.boolean().default(false).describe('Apply filtering recursively to nested objects'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, properties, filter_mode, recursive }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              filtered_json: null,
              error_message: parseResult.error_message,
              properties_processed: 0,
            };
          }

          if (typeof parseResult.data !== 'object' || Array.isArray(parseResult.data) || parseResult.data === null) {
            return {
              success: false,
              filtered_json: null,
              error_message: 'Input is not a JSON object',
              properties_processed: 0,
            };
          }

          // For include mode, build a new object with only specified properties
          if (filter_mode === 'include') {
            const selectParts = properties.map((prop, index) =>
              `'${prop}', ($1::jsonb -> '${prop}')`
            ).join(', ');

            if (selectParts) {
              const query = `SELECT jsonb_build_object(${selectParts}) as filtered_result`;
              const result = await platform.query(query, [json_data]);

              if (result.rows.length > 0) {
                return {
                  success: true,
                  filtered_json: result.rows[0].filtered_result,
                  error_message: null,
                  properties_processed: properties.length,
                  filter_details: {
                    mode: filter_mode,
                    recursive_enabled: recursive,
                    properties_specified: properties.length,
                  },
                };
              }
            }
          } else {
            // For exclude mode, remove specified properties
            let query = `SELECT $1::jsonb`;
            const params = [json_data];

            for (const prop of properties) {
              query = `SELECT (${query}) - '${prop}'`;
            }

            query += ' as filtered_result';

            const result = await platform.query(query, params);

            if (result.rows.length > 0) {
              return {
                success: true,
                filtered_json: result.rows[0].filtered_result,
                error_message: null,
                properties_processed: properties.length,
                filter_details: {
                  mode: filter_mode,
                  recursive_enabled: recursive,
                  properties_specified: properties.length,
                },
              };
            }
          }

          return {
            success: false,
            filtered_json: null,
            error_message: 'No result returned from filter operation',
            properties_processed: 0,
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            filtered_json: null,
            error_message: errorMessage,
            properties_processed: 0,
          };
        }
      },
    }),

    flatten_json_object: injectableTool({
      description: 'Flatten nested JSON objects into a single-level object with dot-notation keys.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON object string to flatten'),
        separator: z.string().default('.').describe('Separator for flattened keys'),
        max_depth: z.number().min(1).max(20).default(10).describe('Maximum depth to flatten'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, separator, max_depth }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              flattened_json: null,
              error_message: parseResult.error_message,
              keys_flattened: 0,
            };
          }

          if (typeof parseResult.data !== 'object' || Array.isArray(parseResult.data) || parseResult.data === null) {
            return {
              success: false,
              flattened_json: null,
              error_message: 'Input is not a JSON object',
              keys_flattened: 0,
            };
          }

          // For now, we'll implement basic flattening using JavaScript logic
          // In a production environment, you might want to use a PostgreSQL extension or custom function
          function flattenObject(obj: any, prefix = '', depth = 0): Record<string, any> {
            const flattened: Record<string, any> = {};

            if (depth >= max_depth) {
              flattened[prefix || 'root'] = obj;
              return flattened;
            }

            for (const [key, value] of Object.entries(obj)) {
              const newKey = prefix ? `${prefix}${separator}${key}` : key;

              if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
                Object.assign(flattened, flattenObject(value, newKey, depth + 1));
              } else {
                flattened[newKey] = value;
              }
            }

            return flattened;
          }

          const flattenedData = flattenObject(parseResult.data);
          const keysFlattened = Object.keys(flattenedData).length;

          return {
            success: true,
            flattened_json: flattenedData,
            error_message: null,
            keys_flattened: keysFlattened,
            flatten_details: {
              separator_used: separator,
              max_depth_allowed: max_depth,
              original_structure_depth: 'unknown', // Would need recursive analysis
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            flattened_json: null,
            error_message: errorMessage,
            keys_flattened: 0,
          };
        }
      },
    }),

    transform_json_values: injectableTool({
      description: 'Transform JSON values based on specified rules. Apply transformations to specific properties or all values.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to transform'),
        transformations: z.array(z.object({
          property_path: z.string().describe('Path to property to transform (use "*" for all)'),
          transformation_type: z.enum(['uppercase', 'lowercase', 'trim', 'prefix', 'suffix', 'replace']).describe('Type of transformation'),
          transformation_value: z.string().optional().describe('Value for prefix, suffix, or replace operations'),
          find_value: z.string().optional().describe('Value to find for replace operations'),
        })).describe('Array of transformation specifications'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, transformations }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              transformed_json: null,
              error_message: parseResult.error_message,
              transformations_applied: 0,
            };
          }

          let transformedData = parseResult.data;
          let transformationsApplied = 0;
          const errors: string[] = [];

          // Apply each transformation
          for (const transform of transformations) {
            try {
              // For simplicity, we'll implement basic string transformations
              // In a full implementation, you'd want more sophisticated path-based transformations
              if (transform.property_path === '*') {
                // Transform all string values
                transformedData = transformAllStringValues(transformedData, transform);
                transformationsApplied++;
              } else {
                // Transform specific property (basic implementation)
                const pathParts = transform.property_path.split('.');
                if (pathParts.length === 1 && typeof transformedData === 'object' && transformedData !== null) {
                  const key = pathParts[0];
                  if (key in transformedData && typeof (transformedData as any)[key] === 'string') {
                    (transformedData as any)[key] = applyStringTransformation((transformedData as any)[key], transform);
                    transformationsApplied++;
                  }
                }
              }
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              errors.push(`Transformation failed for ${transform.property_path}: ${errorMessage}`);
            }
          }

          return {
            success: errors.length === 0,
            transformed_json: transformedData,
            error_message: errors.length > 0 ? errors.join('; ') : null,
            transformations_applied: transformationsApplied,
            transformation_details: {
              total_transformations_requested: transformations.length,
              successful_transformations: transformationsApplied,
              failed_transformations: errors.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            transformed_json: null,
            error_message: errorMessage,
            transformations_applied: 0,
          };
        }
      },
    }),
  };

  // Helper functions for transformations
  function transformAllStringValues(obj: any, transform: any): any {
    if (typeof obj === 'string') {
      return applyStringTransformation(obj, transform);
    } else if (Array.isArray(obj)) {
      return obj.map(item => transformAllStringValues(item, transform));
    } else if (obj !== null && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = transformAllStringValues(value, transform);
      }
      return result;
    }
    return obj;
  }

  function applyStringTransformation(value: string, transform: any): string {
    switch (transform.transformation_type) {
      case 'uppercase':
        return value.toUpperCase();
      case 'lowercase':
        return value.toLowerCase();
      case 'trim':
        return value.trim();
      case 'prefix':
        return (transform.transformation_value || '') + value;
      case 'suffix':
        return value + (transform.transformation_value || '');
      case 'replace':
        return value.replace(
          new RegExp(transform.find_value || '', 'g'),
          transform.transformation_value || ''
        );
      default:
        return value;
    }
  }

  // Add JSON querying and search tools
  const queryingTools = {
    query_json_data: injectableTool({
      description: 'Query JSON data using PostgreSQL JSON path expressions. Supports complex conditions and pattern matching.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to query'),
        query_expression: z.string().describe('PostgreSQL JSON path expression (e.g., "$.users[*].name")'),
        filter_condition: z.string().optional().describe('Optional filter condition for the query'),
        return_type: z.enum(['value', 'exists', 'count']).default('value').describe('Type of result to return'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, query_expression, filter_condition, return_type }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              results: null,
              error_message: parseResult.error_message,
              query_executed: false,
            };
          }

          let query: string;
          let params: any[];

          switch (return_type) {
            case 'exists':
              query = `SELECT jsonb_path_exists($1::jsonb, $2::jsonpath) as result`;
              params = [json_data, query_expression];
              break;
            case 'count':
              query = `SELECT jsonb_path_query_array($1::jsonb, $2::jsonpath) -> 'length' as result`;
              params = [json_data, query_expression];
              break;
            default: // 'value'
              if (filter_condition) {
                query = `SELECT jsonb_path_query_array($1::jsonb, $2::jsonpath, $3::jsonb) as result`;
                params = [json_data, query_expression, `{"filter": "${filter_condition}"}`];
              } else {
                query = `SELECT jsonb_path_query_array($1::jsonb, $2::jsonpath) as result`;
                params = [json_data, query_expression];
              }
              break;
          }

          const result = await platform.query(query, params);

          if (result.rows.length === 0) {
            return {
              success: false,
              results: null,
              error_message: 'No result returned from query',
              query_executed: true,
            };
          }

          return {
            success: true,
            results: result.rows[0].result,
            error_message: null,
            query_executed: true,
            query_details: {
              expression_used: query_expression,
              filter_applied: !!filter_condition,
              return_type,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            results: null,
            error_message: errorMessage,
            query_executed: false,
          };
        }
      },
    }),

    search_json_content: injectableTool({
      description: 'Search for specific values or patterns within JSON data. Supports text search and value matching.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to search'),
        search_term: z.string().describe('Term or pattern to search for'),
        search_type: z.enum(['exact', 'contains', 'regex', 'starts_with', 'ends_with']).default('contains').describe('Type of search to perform'),
        case_sensitive: z.boolean().default(false).describe('Whether search should be case sensitive'),
        search_keys: z.boolean().default(false).describe('Whether to search in object keys as well as values'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, search_term, search_type, case_sensitive, search_keys }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              matches: [],
              error_message: parseResult.error_message,
              search_executed: false,
            };
          }

          // Build search query based on search type
          let searchQuery: string;
          let searchParams: any[];

          const caseSensitiveFlag = case_sensitive ? '' : 'i';

          switch (search_type) {
            case 'exact':
              searchQuery = `
                WITH RECURSIVE json_search AS (
                  SELECT $1::jsonb as data, '' as path
                  UNION ALL
                  SELECT value, path || '.' || key
                  FROM json_search, jsonb_each(data)
                  WHERE jsonb_typeof(data) = 'object'
                )
                SELECT path, data::text as value
                FROM json_search
                WHERE data::text ${case_sensitive ? '=' : 'ILIKE'} $2
              `;
              searchParams = [json_data, case_sensitive ? search_term : search_term];
              break;

            case 'contains':
              searchQuery = `
                WITH RECURSIVE json_search AS (
                  SELECT $1::jsonb as data, '' as path
                  UNION ALL
                  SELECT value, path || '.' || key
                  FROM json_search, jsonb_each(data)
                  WHERE jsonb_typeof(data) = 'object'
                )
                SELECT path, data::text as value
                FROM json_search
                WHERE data::text ${case_sensitive ? 'LIKE' : 'ILIKE'} $2
              `;
              searchParams = [json_data, `%${search_term}%`];
              break;

            case 'regex':
              searchQuery = `
                WITH RECURSIVE json_search AS (
                  SELECT $1::jsonb as data, '' as path
                  UNION ALL
                  SELECT value, path || '.' || key
                  FROM json_search, jsonb_each(data)
                  WHERE jsonb_typeof(data) = 'object'
                )
                SELECT path, data::text as value
                FROM json_search
                WHERE data::text ~${case_sensitive ? '' : '*'} $2
              `;
              searchParams = [json_data, search_term];
              break;

            default:
              // Fallback to contains
              searchQuery = `
                WITH RECURSIVE json_search AS (
                  SELECT $1::jsonb as data, '' as path
                  UNION ALL
                  SELECT value, path || '.' || key
                  FROM json_search, jsonb_each(data)
                  WHERE jsonb_typeof(data) = 'object'
                )
                SELECT path, data::text as value
                FROM json_search
                WHERE data::text ILIKE $2
              `;
              searchParams = [json_data, `%${search_term}%`];
              break;
          }

          const result = await platform.query(searchQuery, searchParams);

          const matches = result.rows.map(row => ({
            path: row.path || 'root',
            value: row.value,
            match_type: 'value',
          }));

          return {
            success: true,
            matches,
            error_message: null,
            search_executed: true,
            search_details: {
              search_term,
              search_type,
              case_sensitive,
              search_keys,
              total_matches: matches.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            matches: [],
            error_message: errorMessage,
            search_executed: false,
          };
        }
      },
    }),

    filter_json_array: injectableTool({
      description: 'Filter JSON arrays based on element properties and conditions. Extract matching elements from arrays.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string containing arrays to filter'),
        array_path: z.string().describe('Path to the array to filter (e.g., "$.users")'),
        filter_conditions: z.array(z.object({
          property: z.string().describe('Property name to filter on'),
          operator: z.enum(['=', '!=', '>', '<', '>=', '<=', 'contains', 'starts_with', 'ends_with']).describe('Comparison operator'),
          value: z.unknown().describe('Value to compare against'),
        })).describe('Array of filter conditions'),
        condition_logic: z.enum(['AND', 'OR']).default('AND').describe('Logic to combine multiple conditions'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, array_path, filter_conditions, condition_logic }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              filtered_array: [],
              error_message: parseResult.error_message,
              elements_matched: 0,
            };
          }

          // Extract the array first
          const arrayExtractionResult = await jsonHandlingTools.extract_json_value.execute({
            project_id,
            json_data,
            path: array_path,
            default_value: [],
          });

          if (!arrayExtractionResult.success || !arrayExtractionResult.path_found) {
            return {
              success: false,
              filtered_array: [],
              error_message: `Array not found at path: ${array_path}`,
              elements_matched: 0,
            };
          }

          const arrayData = arrayExtractionResult.value;
          if (!Array.isArray(arrayData)) {
            return {
              success: false,
              filtered_array: [],
              error_message: 'Extracted data is not an array',
              elements_matched: 0,
            };
          }

          // Apply filters using JavaScript logic (in a production environment, you'd use PostgreSQL JSON functions)
          const filteredElements = arrayData.filter((element: any) => {
            if (typeof element !== 'object' || element === null) {
              return false;
            }

            const conditionResults = filter_conditions.map(condition => {
              const elementValue = element[condition.property];
              const filterValue = condition.value;

              switch (condition.operator) {
                case '=':
                  return elementValue === filterValue;
                case '!=':
                  return elementValue !== filterValue;
                case '>':
                  return elementValue > filterValue;
                case '<':
                  return elementValue < filterValue;
                case '>=':
                  return elementValue >= filterValue;
                case '<=':
                  return elementValue <= filterValue;
                case 'contains':
                  return typeof elementValue === 'string' && typeof filterValue === 'string' &&
                         elementValue.includes(filterValue);
                case 'starts_with':
                  return typeof elementValue === 'string' && typeof filterValue === 'string' &&
                         elementValue.startsWith(filterValue);
                case 'ends_with':
                  return typeof elementValue === 'string' && typeof filterValue === 'string' &&
                         elementValue.endsWith(filterValue);
                default:
                  return false;
              }
            });

            // Apply condition logic
            if (condition_logic === 'OR') {
              return conditionResults.some(result => result);
            } else {
              return conditionResults.every(result => result);
            }
          });

          return {
            success: true,
            filtered_array: filteredElements,
            error_message: null,
            elements_matched: filteredElements.length,
            filter_details: {
              original_array_length: arrayData.length,
              conditions_applied: filter_conditions.length,
              condition_logic,
              array_path,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            filtered_array: [],
            error_message: errorMessage,
            elements_matched: 0,
          };
        }
      },
    }),
  };

  // Add JSON repair and sanitization tools
  const repairTools = {
    repair_malformed_json: injectableTool({
      description: 'Attempt to repair common JSON syntax errors and return valid JSON. Handles missing quotes, trailing commas, and other issues.',
      parameters: z.object({
        project_id: z.string(),
        malformed_json: z.string().describe('Malformed JSON string to repair'),
        repair_strategy: z.enum(['aggressive', 'conservative', 'minimal']).default('conservative').describe('How aggressively to attempt repairs'),
        preserve_structure: z.boolean().default(true).describe('Try to preserve original structure when possible'),
      }),
      inject: { project_id },
      execute: async ({ project_id, malformed_json, repair_strategy, preserve_structure }) => {
        try {
          // First check if it's already valid JSON
          const validationResult = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data: malformed_json,
            check_encoding: true,
          });

          if (validationResult.is_valid) {
            return {
              success: true,
              repaired_json: malformed_json,
              repairs_applied: [],
              error_message: null,
              was_already_valid: true,
            };
          }

          let repairedJson = malformed_json;
          const repairsApplied: string[] = [];

          // Apply repairs based on strategy
          if (repair_strategy === 'aggressive' || repair_strategy === 'conservative') {
            // Fix trailing commas
            if (repairedJson.includes(',}') || repairedJson.includes(',]')) {
              repairedJson = repairedJson.replace(/,(\s*[}\]])/g, '$1');
              repairsApplied.push('Removed trailing commas');
            }

            // Fix unquoted keys (common JavaScript object notation)
            const unquotedKeyRegex = /(\{|\,)\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g;
            if (unquotedKeyRegex.test(repairedJson)) {
              repairedJson = repairedJson.replace(unquotedKeyRegex, '$1"$2":');
              repairsApplied.push('Added quotes to unquoted keys');
            }

            // Fix single quotes to double quotes
            if (repairedJson.includes("'")) {
              // Be careful not to replace single quotes inside double-quoted strings
              repairedJson = repairedJson.replace(/'([^']*?)'/g, '"$1"');
              repairsApplied.push('Converted single quotes to double quotes');
            }
          }

          if (repair_strategy === 'aggressive') {
            // More aggressive repairs

            // Try to fix missing closing brackets/braces
            const openBraces = (repairedJson.match(/{/g) || []).length;
            const closeBraces = (repairedJson.match(/}/g) || []).length;
            if (openBraces > closeBraces) {
              repairedJson += '}'.repeat(openBraces - closeBraces);
              repairsApplied.push(`Added ${openBraces - closeBraces} missing closing braces`);
            }

            const openBrackets = (repairedJson.match(/\[/g) || []).length;
            const closeBrackets = (repairedJson.match(/]/g) || []).length;
            if (openBrackets > closeBrackets) {
              repairedJson += ']'.repeat(openBrackets - closeBrackets);
              repairsApplied.push(`Added ${openBrackets - closeBrackets} missing closing brackets`);
            }

            // Remove extra closing brackets/braces
            if (closeBraces > openBraces) {
              const extraBraces = closeBraces - openBraces;
              for (let i = 0; i < extraBraces; i++) {
                const lastBraceIndex = repairedJson.lastIndexOf('}');
                if (lastBraceIndex !== -1) {
                  repairedJson = repairedJson.substring(0, lastBraceIndex) + repairedJson.substring(lastBraceIndex + 1);
                }
              }
              repairsApplied.push(`Removed ${extraBraces} extra closing braces`);
            }

            if (closeBrackets > openBrackets) {
              const extraBrackets = closeBrackets - openBrackets;
              for (let i = 0; i < extraBrackets; i++) {
                const lastBracketIndex = repairedJson.lastIndexOf(']');
                if (lastBracketIndex !== -1) {
                  repairedJson = repairedJson.substring(0, lastBracketIndex) + repairedJson.substring(lastBracketIndex + 1);
                }
              }
              repairsApplied.push(`Removed ${extraBrackets} extra closing brackets`);
            }
          }

          // Test if repairs were successful
          const finalValidation = await jsonHandlingTools.validate_json_syntax.execute({
            project_id,
            json_data: repairedJson,
            check_encoding: false,
          });

          return {
            success: finalValidation.is_valid,
            repaired_json: finalValidation.is_valid ? repairedJson : null,
            repairs_applied: repairsApplied,
            error_message: finalValidation.is_valid ? null : finalValidation.error_message,
            was_already_valid: false,
            repair_details: {
              strategy_used: repair_strategy,
              preserve_structure,
              original_length: malformed_json.length,
              repaired_length: repairedJson.length,
              repairs_count: repairsApplied.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            repaired_json: null,
            repairs_applied: [],
            error_message: errorMessage,
            was_already_valid: false,
          };
        }
      },
    }),

    sanitize_json_data: injectableTool({
      description: 'Sanitize JSON data by removing potentially harmful content and normalizing values.',
      parameters: z.object({
        project_id: z.string(),
        json_data: z.string().describe('JSON string to sanitize'),
        sanitization_rules: z.array(z.enum(['remove_null_values', 'trim_strings', 'remove_empty_objects', 'remove_empty_arrays', 'normalize_numbers', 'escape_html'])).default(['trim_strings']).describe('Sanitization rules to apply'),
        max_string_length: z.number().min(1).max(10000).default(1000).describe('Maximum allowed string length'),
      }),
      inject: { project_id },
      execute: async ({ project_id, json_data, sanitization_rules, max_string_length }) => {
        try {
          // Validate JSON first
          const parseResult = await jsonHandlingTools.safe_json_parse.execute({
            project_id,
            json_data,
            return_raw_on_error: false,
          });

          if (!parseResult.success) {
            return {
              success: false,
              sanitized_json: null,
              error_message: parseResult.error_message,
              sanitizations_applied: [],
            };
          }

          let sanitizedData = parseResult.data;
          const sanitizationsApplied: string[] = [];

          // Apply sanitization rules
          sanitizedData = applySanitizationRules(sanitizedData, sanitization_rules, max_string_length, sanitizationsApplied);

          return {
            success: true,
            sanitized_json: sanitizedData,
            error_message: null,
            sanitizations_applied: sanitizationsApplied,
            sanitization_details: {
              rules_applied: sanitization_rules,
              max_string_length,
              original_size: JSON.stringify(parseResult.data).length,
              sanitized_size: JSON.stringify(sanitizedData).length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            sanitized_json: null,
            error_message: errorMessage,
            sanitizations_applied: [],
          };
        }
      },
    }),

    recover_partial_json: injectableTool({
      description: 'Attempt to recover usable data from severely malformed JSON by extracting valid portions.',
      parameters: z.object({
        project_id: z.string(),
        malformed_json: z.string().describe('Severely malformed JSON string'),
        recovery_mode: z.enum(['extract_objects', 'extract_arrays', 'extract_values', 'best_effort']).default('best_effort').describe('Type of recovery to attempt'),
        min_recovery_size: z.number().min(1).default(10).describe('Minimum size of recovered data to be considered valid'),
      }),
      inject: { project_id },
      execute: async ({ project_id, malformed_json, recovery_mode, min_recovery_size }) => {
        try {
          const recoveredData: any[] = [];
          const recoveryLog: string[] = [];

          // Try different recovery strategies based on mode
          switch (recovery_mode) {
            case 'extract_objects':
              // Look for object-like patterns
              const objectMatches = malformed_json.match(/\{[^{}]*\}/g);
              if (objectMatches) {
                for (const match of objectMatches) {
                  try {
                    const parsed = JSON.parse(match);
                    if (JSON.stringify(parsed).length >= min_recovery_size) {
                      recoveredData.push(parsed);
                      recoveryLog.push(`Recovered object: ${match.substring(0, 50)}...`);
                    }
                  } catch {
                    // Try to repair this object
                    const repairResult = await repairTools.repair_malformed_json.execute({
                      project_id,
                      malformed_json: match,
                      repair_strategy: 'aggressive',
                      preserve_structure: true,
                    });
                    if (repairResult.success && repairResult.repaired_json) {
                      try {
                        const parsed = JSON.parse(repairResult.repaired_json);
                        recoveredData.push(parsed);
                        recoveryLog.push(`Recovered and repaired object: ${match.substring(0, 50)}...`);
                      } catch {
                        // Skip this one
                      }
                    }
                  }
                }
              }
              break;

            case 'extract_arrays':
              // Look for array-like patterns
              const arrayMatches = malformed_json.match(/\[[^\[\]]*\]/g);
              if (arrayMatches) {
                for (const match of arrayMatches) {
                  try {
                    const parsed = JSON.parse(match);
                    if (JSON.stringify(parsed).length >= min_recovery_size) {
                      recoveredData.push(parsed);
                      recoveryLog.push(`Recovered array: ${match.substring(0, 50)}...`);
                    }
                  } catch {
                    // Try to repair this array
                    const repairResult = await repairTools.repair_malformed_json.execute({
                      project_id,
                      malformed_json: match,
                      repair_strategy: 'aggressive',
                      preserve_structure: true,
                    });
                    if (repairResult.success && repairResult.repaired_json) {
                      try {
                        const parsed = JSON.parse(repairResult.repaired_json);
                        recoveredData.push(parsed);
                        recoveryLog.push(`Recovered and repaired array: ${match.substring(0, 50)}...`);
                      } catch {
                        // Skip this one
                      }
                    }
                  }
                }
              }
              break;

            case 'extract_values':
              // Look for quoted strings and numbers
              const stringMatches = malformed_json.match(/"[^"]*"/g);
              if (stringMatches) {
                for (const match of stringMatches) {
                  if (match.length >= min_recovery_size) {
                    recoveredData.push(match.slice(1, -1)); // Remove quotes
                    recoveryLog.push(`Recovered string: ${match}`);
                  }
                }
              }

              const numberMatches = malformed_json.match(/\b\d+\.?\d*\b/g);
              if (numberMatches) {
                for (const match of numberMatches) {
                  const num = parseFloat(match);
                  if (!isNaN(num)) {
                    recoveredData.push(num);
                    recoveryLog.push(`Recovered number: ${match}`);
                  }
                }
              }
              break;

            default: // 'best_effort'
              // Try all recovery methods
              const allMethods = ['extract_objects', 'extract_arrays', 'extract_values'];
              for (const method of allMethods) {
                const methodResult = await repairTools.recover_partial_json.execute({
                  project_id,
                  malformed_json,
                  recovery_mode: method as any,
                  min_recovery_size,
                });
                if (methodResult.success && methodResult.recovered_data.length > 0) {
                  recoveredData.push(...methodResult.recovered_data);
                  recoveryLog.push(...methodResult.recovery_log);
                }
              }
              break;
          }

          return {
            success: recoveredData.length > 0,
            recovered_data: recoveredData,
            recovery_log: recoveryLog,
            error_message: recoveredData.length === 0 ? 'No recoverable data found' : null,
            recovery_details: {
              mode_used: recovery_mode,
              min_size_threshold: min_recovery_size,
              items_recovered: recoveredData.length,
              original_length: malformed_json.length,
            },
          };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return {
            success: false,
            recovered_data: [],
            recovery_log: [],
            error_message: errorMessage,
            recovery_details: {
              mode_used: recovery_mode,
              min_size_threshold: min_recovery_size,
              items_recovered: 0,
              original_length: malformed_json.length,
            },
          };
        }
      },
    }),
  };

  // Helper function for sanitization
  function applySanitizationRules(data: any, rules: string[], maxStringLength: number, appliedRules: string[]): any {
    if (data === null || data === undefined) {
      if (rules.includes('remove_null_values')) {
        appliedRules.push('Removed null values');
        return undefined;
      }
      return data;
    }

    if (typeof data === 'string') {
      let result = data;

      if (rules.includes('trim_strings')) {
        const trimmed = result.trim();
        if (trimmed !== result) {
          appliedRules.push('Trimmed strings');
          result = trimmed;
        }
      }

      if (result.length > maxStringLength) {
        result = result.substring(0, maxStringLength);
        appliedRules.push(`Truncated strings to ${maxStringLength} characters`);
      }

      if (rules.includes('escape_html')) {
        const escaped = result
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#x27;');
        if (escaped !== result) {
          appliedRules.push('Escaped HTML characters');
          result = escaped;
        }
      }

      return result;
    }

    if (typeof data === 'number') {
      if (rules.includes('normalize_numbers')) {
        if (!isFinite(data)) {
          appliedRules.push('Normalized infinite/NaN numbers');
          return 0;
        }
      }
      return data;
    }

    if (Array.isArray(data)) {
      const sanitizedArray = data
        .map(item => applySanitizationRules(item, rules, maxStringLength, appliedRules))
        .filter(item => item !== undefined);

      if (rules.includes('remove_empty_arrays') && sanitizedArray.length === 0) {
        appliedRules.push('Removed empty arrays');
        return undefined;
      }

      return sanitizedArray;
    }

    if (typeof data === 'object') {
      const sanitizedObject: any = {};
      let hasProperties = false;

      for (const [key, value] of Object.entries(data)) {
        const sanitizedValue = applySanitizationRules(value, rules, maxStringLength, appliedRules);
        if (sanitizedValue !== undefined) {
          sanitizedObject[key] = sanitizedValue;
          hasProperties = true;
        }
      }

      if (rules.includes('remove_empty_objects') && !hasProperties) {
        appliedRules.push('Removed empty objects');
        return undefined;
      }

      return sanitizedObject;
    }

    return data;
  }

  // Merge repair tools with main tools
  Object.assign(jsonHandlingTools, queryingTools, repairTools);

  return jsonHandlingTools;
}
