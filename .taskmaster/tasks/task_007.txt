# Task ID: 7
# Title: Constraint Validation Tool
# Status: done
# Dependencies: 4, 5
# Priority: medium
# Description: Implement MCP tool for validating foreign key, check, and unique constraints.
# Details:
Implemented comprehensive constraint validation tools for the Supabase MCP server, including four core tools: validate_foreign_key_constraints, validate_unique_constraints, validate_check_constraints, and validate_all_constraints. Each tool provides detailed violation reporting with configurable limits, parameter validation using zod schemas, and comprehensive error handling. The implementation follows existing MCP server patterns for validation, error handling, and response formatting.

# Test Strategy:
Comprehensive testing completed with 14 test cases covering functional validation for all constraint types, parameter validation, error handling for non-existent schemas/tables, and edge cases. All tests are passing (14/14).

# Subtasks:
## 7.1. Implement validate_foreign_key_constraints tool [completed]
### Dependencies: None
### Description: Created tool to validate foreign key constraints and detect orphaned records using information_schema queries
### Details:


## 7.2. Implement validate_unique_constraints tool [completed]
### Dependencies: None
### Description: Created tool to validate unique and primary key constraints and detect duplicate values
### Details:


## 7.3. Implement validate_check_constraints tool [completed]
### Dependencies: None
### Description: Created tool to validate check constraints and detect constraint violations
### Details:


## 7.4. Implement validate_all_constraints tool [completed]
### Dependencies: None
### Description: Created comprehensive tool that validates all constraint types and provides a summary
### Details:


## 7.5. Implement parameter validation [completed]
### Dependencies: None
### Description: Added zod schemas for validating schema names, table names, and constraint names
### Details:


## 7.6. Implement configurable violation reporting [completed]
### Dependencies: None
### Description: Added support for configurable violation detail reporting with limit controls
### Details:


## 7.7. Create integration tests [completed]
### Dependencies: None
### Description: Developed comprehensive integration tests (300+ lines) with 14 test cases covering all functionality
### Details:


## 7.8. Integrate tools into MCP server [completed]
### Dependencies: None
### Description: Added tools to server.ts with proper metadata and categorization
### Details:


