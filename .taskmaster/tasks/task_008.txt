# Task ID: 8
# Title: JSON Handling Tool
# Status: done
# Dependencies: 4, 5
# Priority: medium
# Description: Implement MCP tool for complex JSON operations and malformed data handling.
# Details:
Use PostgreSQL JSON functions. Validate and sanitize input. Expose as MCP tool.

# Test Strategy:
Test with valid and malformed JSON. Verify error handling and output.

# Subtasks:
## 1. Implement JSON validation and syntax checking [done]
### Dependencies: None
### Description: Create functions to validate JSON syntax and structure against schemas. Handle common validation scenarios like required fields, data types, and nested structure validation.
### Details:
Implement isValidJson() to check basic syntax, validateJsonSchema() to validate against a schema definition, and getValidationErrors() to return detailed error information. Use PostgreSQL's json_typeof() and json_valid() functions where appropriate. Include support for JSON Schema validation standards.

## 2. Develop JSON parsing and extraction utilities [done]
### Dependencies: 8.1
### Description: Create utilities to safely parse JSON strings and extract values from complex nested structures using path expressions.
### Details:
Implement safeJsonParse() that won't throw exceptions, getValueByPath() for dot-notation path extraction (e.g., 'user.address.city'), and extractValues() to pull multiple values in one operation. Use PostgreSQL's json_extract_path_text() and similar functions. Handle edge cases like missing paths gracefully.

## 3. Build JSON transformation and manipulation functions [done]
### Dependencies: 8.2
### Description: Create functions to transform JSON structures, including filtering, mapping, merging, and restructuring operations.
### Details:
Implement filterJson() to remove unwanted properties, mapJson() to transform values, mergeJson() to combine objects, flattenJson() to simplify nested structures, and restructureJson() to reorganize data. Use PostgreSQL's jsonb_set(), jsonb_insert(), and other manipulation functions. Support both shallow and deep operations.

## 4. Implement JSON querying and search capabilities [done]
### Dependencies: 8.2
### Description: Create advanced querying functions to search within JSON data, supporting complex conditions and pattern matching.
### Details:
Implement queryJson() with support for complex conditions, findInJson() for recursive searching, and jsonArrayFilter() for filtering arrays based on element properties. Leverage PostgreSQL's jsonb_path_query() and similar functions. Support both exact and fuzzy matching options.

## 5. Develop malformed JSON repair and sanitization [done]
### Dependencies: 8.1, 8.2
### Description: Create utilities to detect, repair, and sanitize malformed JSON data, recovering usable data when possible.
### Details:
Implement repairJson() to fix common syntax errors like missing quotes or commas, sanitizeJson() to remove potentially harmful content, and recoverPartialJson() to extract valid portions from invalid JSON. Include options for strict or lenient handling. Use regex and string manipulation techniques combined with PostgreSQL's sanitization functions.

## 6. Expose JSON tools as MCP interface [done]
### Dependencies: 8.1, 8.2, 8.3, 8.4, 8.5
### Description: Package all JSON handling functions into a cohesive MCP tool interface with documentation and examples.
### Details:
Create a unified MCP.json namespace with organized function categories. Include comprehensive documentation with examples for each function. Implement consistent error handling and logging. Add performance optimizations for large JSON operations. Create helper functions for common Supabase JSON operations.

